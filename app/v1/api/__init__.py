from fastapi import FastAPI

from .users import router as user_router
from .config import router as config_router
from .roles import router as role_router
from .permissions import router as permissions_router
from .prompts import router as prompts_router
from .llmcall import router as llmcall_router


router = FastAPI(title="API n Prompts API v1")

router.include_router(user_router)
router.include_router(config_router)
router.include_router(prompts_router)
router.include_router(llmcall_router)
# router.include_router(role_router)
# router.include_router(permissions_router)
