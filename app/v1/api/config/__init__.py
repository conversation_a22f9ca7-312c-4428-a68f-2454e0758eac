from fastapi import APIRout<PERSON>, Depends, HTTPException, Body
from bson import ObjectId

from app.core.helper import logger
from app.core.security import get_tenant_info, require_permissions
from app.models.user import UserTenantDB

loggers = logger.setup_new_logging(__name__)

router = APIRouter(tags=["Config"])

# @router.get("/configs")
# async def get_all_configs(
#     current_user: UserTenantDB = Depends(get_tenant_info)
# ):
#     """Get all configurations from the config collection"""
#     try:
#         configs_collection = current_user.db.config
#         configs = list(configs_collection.find({}))
        
#         # Convert ObjectId to string for JSON serialization
#         for config in configs:
#             config["_id"] = str(config["_id"])
        
#         return {"configs": configs}
#     except Exception as e:
#         loggers.error(f"Error fetching configs: {str(e)}")
#         raise HTTPException(status_code=500, detail="Error fetching configurations")

@router.post("/configs/google-api-key")
async def set_google_api_key(
    google_api_key: str = Body(..., embed=True),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """Set the Google API key in the config collection"""
    try:
        configs_collection = current_user.db.config
        result = configs_collection.update_one(
            {"name": "env"},
            {"$set": {"values": {"GOOGLE_API_KEY":google_api_key}}}
        )
        if result.modified_count > 0 or result.upserted_id:
            return {"message": "Google API key set successfully"}
        else:
            return {"message": "Google API key was already set to this value"}
    except Exception as e:
        loggers.error(f"Error setting Google API key: {str(e)}")
        raise HTTPException(status_code=500, detail="Error setting Google API key")

@router.get("/configs/google-api-key")
async def get_google_api_key(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """Get the Google API key from the config collection"""
    try:
        configs_collection = current_user.db.config
        config = configs_collection.find_one({"name": "env"})
        if config and "values" in config:
            return config.get("values")
        else:
            return {"message": "Google API key not set"}
    except Exception as e:
        loggers.error(f"Error fetching Google API key: {str(e)}")
        raise HTTPException(status_code=500, detail="Error fetching Google API key")



