from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Body
from fastapi.responses import StreamingResponse
from bson import ObjectId
import httpx
import json
import io

from app.core.helper import logger
from app.core.security import get_tenant_info, require_permissions
from app.models.user import UserTenantDB

loggers = logger.setup_new_logging(__name__)

router = APIRouter(tags=["Prompts"])

@router.get("/prompts")
async def get_all_prompts(current_user: UserTenantDB = Depends(get_tenant_info)):
    """Get all prompts from the prompts collection"""
    try:
        prompts_collection = current_user.db.prompts
        prompts = list(prompts_collection.find({}))
        for prompt in prompts:
            prompt["_id"] = str(prompt["_id"])
        return {"prompts": prompts}
    except Exception as e:
        loggers.error(f"Error fetching prompts: {str(e)}")
        raise HTTPException(status_code=500, detail="Error fetching prompts")

@router.post("/prompts")
async def add_prompt(
    name: str = Body(...),
    text: str = Body(...),
    model: str = Body(...),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """Add a new prompt to the prompts collection"""
    try:
        prompts_collection = current_user.db.prompts
        prompt = {"name": name, "text": text, "model": model}
        result = prompts_collection.insert_one(prompt)
        return {"message": "Prompt added successfully", "prompt_id": str(result.inserted_id)}
    except Exception as e:
        loggers.error(f"Error adding prompt: {str(e)}")
        raise HTTPException(status_code=500, detail="Error adding prompt")

@router.put("/prompts/{prompt_id}")
async def update_prompt(
    prompt_id: str,
    name: str = Body(None),
    text: str = Body(None),
    model: str = Body(None),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """Update a prompt by ID"""
    try:
        prompts_collection = current_user.db.prompts
        update_data = {k: v for k, v in {"name": name, "text": text, "model": model}.items() if v is not None}
        if not update_data:
            raise HTTPException(status_code=400, detail="No update fields provided")
        result = prompts_collection.update_one({"_id": ObjectId(prompt_id)}, {"$set": update_data})
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Prompt not found")
        return {"message": "Prompt updated successfully"}
    except Exception as e:
        loggers.error(f"Error updating prompt: {str(e)}")
        raise HTTPException(status_code=500, detail="Error updating prompt")

@router.delete("/prompts/{prompt_id}")
async def delete_prompt(
    prompt_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """Delete a prompt by ID"""
    try:
        prompts_collection = current_user.db.prompts
        result = prompts_collection.delete_one({"_id": ObjectId(prompt_id)})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Prompt not found")
        return {"message": "Prompt deleted successfully"}
    except Exception as e:
        loggers.error(f"Error deleting prompt: {str(e)}")
        raise HTTPException(status_code=500, detail="Error deleting prompt")



@router.get("/voices")
async def get_all_voices(current_user: UserTenantDB = Depends(get_tenant_info)):
    """Get all voices from the prompts collection"""
    try:
        prompts_collection = current_user.db.voices
        prompts = list(prompts_collection.find({}))
        for prompt in prompts:
            prompt["_id"] = str(prompt["_id"])
        return {"voices": prompts}
    except Exception as e:
        loggers.error(f"Error fetching prompts: {str(e)}")
        raise HTTPException(status_code=500, detail="Error fetching prompts")
