from fastapi import APIRouter, Depends, HTTPException, Body
from fastapi.responses import StreamingResponse
import httpx
import json
import io
import base64
import asyncio
from typing import Dict, Any

from app.core.helper import logger
from app.core.security import get_tenant_info
from app.models.user import UserTenantDB

loggers = logger.setup_new_logging(__name__)

router = APIRouter(tags=["LLM Calls"])


async def make_tts_request_with_retry(
    text: str,
    voice_name: str,
    temperature: float,
    max_retries: int = 3,
    base_delay: float = 1.0
) -> tuple[bytes, str]:
    """
    Make TTS API request with exponential backoff retry for rate limiting
    Returns tuple of (audio_data, mime_type)
    """
    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://voicegen.nextai.asia',
        'priority': 'u=1, i',
        'referer': 'https://voicegen.nextai.asia/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
        'x-goog-api-client': 'genai-js/0.24.1',
        'x-goog-api-key': 'AIzaSyAl7A7TVjNrvQ8k0xAsrqF2kIxM0771mQk',
    }

    params = {'alt': 'sse'}

    json_data = {
        'generationConfig': {
            'temperature': temperature,
            'responseModalities': ['AUDIO'],
            'speechConfig': {
                'voiceConfig': {
                    'prebuiltVoiceConfig': {
                        'voiceName': voice_name,
                    },
                },
            },
        },
        'safetySettings': [],
        'contents': [
            {
                'role': 'user',
                'parts': [
                    {
                        'text': f'Read in Nepali accurate accent. Speak Individual words as a teacher would speak, slow paced with certain rhythm. The flow and dictation should be local Nepali. Naration style.\n\n{text}',
                    },
                ],
            },
        ],
    }

    for attempt in range(max_retries + 1):
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro-preview-tts:streamGenerateContent',
                    params=params,
                    headers=headers,
                    json=json_data,
                )

                # Handle rate limiting
                if response.status_code == 429:
                    if attempt < max_retries:
                        # Extract retry-after header if available
                        retry_after = response.headers.get('retry-after')
                        if retry_after:
                            delay = float(retry_after)
                        else:
                            # Exponential backoff: 1s, 2s, 4s, 8s...
                            delay = base_delay * (2 ** attempt)

                        loggers.warning(f"Rate limited (attempt {attempt + 1}/{max_retries + 1}). Retrying in {delay}s...")
                        await asyncio.sleep(delay)
                        continue
                    else:
                        loggers.error("Rate limit exceeded. Max retries reached.")
                        raise HTTPException(
                            status_code=429,
                            detail="Rate limit exceeded. Please try again later."
                        )

                # Handle other HTTP errors
                if response.status_code != 200:
                    error_msg = f"TTS API error: {response.status_code} - {response.text}"
                    loggers.error(error_msg)

                    # Don't retry on client errors (4xx except 429)
                    if 400 <= response.status_code < 500 and response.status_code != 429:
                        raise HTTPException(status_code=response.status_code, detail=error_msg)

                    # Retry on server errors (5xx)
                    if attempt < max_retries and response.status_code >= 500:
                        delay = base_delay * (2 ** attempt)
                        loggers.warning(f"Server error (attempt {attempt + 1}/{max_retries + 1}). Retrying in {delay}s...")
                        await asyncio.sleep(delay)
                        continue

                    raise HTTPException(status_code=response.status_code, detail=error_msg)

                # Process successful response
                audio_data = b""
                mime_type = "audio/wav"  # Default

                for line in response.iter_lines():
                    if line.startswith('data: '):
                        try:
                            json_str = line[6:]  # Remove 'data: ' prefix
                            if json_str.strip() == '[DONE]':
                                break

                            data = json.loads(json_str)

                            if 'candidates' in data:
                                for candidate in data['candidates']:
                                    if 'content' in candidate and 'parts' in candidate['content']:
                                        for part in candidate['content']['parts']:
                                            if 'inlineData' in part and 'data' in part['inlineData']:
                                                if 'mimeType' in part['inlineData']:
                                                    mime_type = part['inlineData']['mimeType']

                                                audio_chunk = base64.b64decode(part['inlineData']['data'])
                                                audio_data += audio_chunk

                        except (json.JSONDecodeError, KeyError) as e:
                            loggers.warning(f"Error parsing SSE data: {e}")
                            continue

                if not audio_data:
                    if attempt < max_retries:
                        delay = base_delay * (2 ** attempt)
                        loggers.warning(f"No audio data received (attempt {attempt + 1}/{max_retries + 1}). Retrying in {delay}s...")
                        await asyncio.sleep(delay)
                        continue
                    else:
                        raise HTTPException(status_code=500, detail="No audio data received from TTS API")

                loggers.info(f"Successfully generated audio: {len(audio_data)} bytes, mime type: {mime_type}")
                return audio_data, mime_type

        except httpx.RequestError as e:
            if attempt < max_retries:
                delay = base_delay * (2 ** attempt)
                loggers.warning(f"Request error (attempt {attempt + 1}/{max_retries + 1}): {e}. Retrying in {delay}s...")
                await asyncio.sleep(delay)
                continue
            else:
                loggers.error(f"Request failed after {max_retries + 1} attempts: {e}")
                raise HTTPException(status_code=500, detail=f"Request error: {str(e)}")

    # This should never be reached, but just in case
    raise HTTPException(status_code=500, detail="Unexpected error in TTS request")

@router.post("/tts/generate")
async def generate_tts_audio(
    text: str = Body(..., description="Text to convert to speech"),
    voice_name: str = Body("Sadaltager", description="Voice name for TTS"),
    temperature: float = Body(1.0, description="Temperature for generation"),
    max_retries: int = Body(3, description="Maximum number of retries for rate limiting"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Generate audio from text using Google's Gemini TTS API with rate limit handling
    """
    try:
        loggers.info(f"TTS request from user: {current_user.user.username} for text: {text[:50]}...")

        # Use the retry function to handle rate limiting
        audio_data, mime_type = await make_tts_request_with_retry(
            text=text,
            voice_name=voice_name,
            temperature=temperature,
            max_retries=max_retries
        )
        # Determine file extension based on mime type
        if "mp3" in mime_type:
            file_ext = "mp3"
        elif "wav" in mime_type:
            file_ext = "wav"
        elif "ogg" in mime_type:
            file_ext = "ogg"
        elif "aac" in mime_type:
            file_ext = "aac"
        else:
            file_ext = "audio"

        # Return the audio as a streaming response
        return StreamingResponse(
            io.BytesIO(audio_data),
            media_type=mime_type,
            headers={
                "Content-Disposition": f"attachment; filename=generated_audio.{file_ext}",
                "Content-Length": str(len(audio_data)),
                "Accept-Ranges": "bytes"
            }
        )

    except HTTPException:
        # Re-raise HTTPExceptions from the retry function
        raise
    except Exception as e:
        loggers.error(f"Unexpected error in TTS generation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/tts/play")
async def play_tts_audio(
    text: str = Body(..., description="Text to convert to speech"),
    voice_name: str = Body("Sadaltager", description="Voice name for TTS"),
    temperature: float = Body(1.0, description="Temperature for generation"),
    max_retries: int = Body(3, description="Maximum number of retries for rate limiting"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Generate audio from text using Google's Gemini TTS API and return for direct playback with rate limit handling
    """
    try:
        loggers.info(f"TTS play request from user: {current_user.user.username} for text: {text[:50]}...")

        # Use the retry function to handle rate limiting
        audio_data, mime_type = await make_tts_request_with_retry(
            text=text,
            voice_name=voice_name,
            temperature=temperature,
            max_retries=max_retries
        )

        # Return the audio for direct playback (inline)
        return StreamingResponse(
            io.BytesIO(audio_data),
            media_type=mime_type,
            headers={
                "Content-Disposition": "inline",
                "Content-Length": str(len(audio_data)),
                "Accept-Ranges": "bytes",
                "Cache-Control": "no-cache"
            }
        )

    except HTTPException:
        # Re-raise HTTPExceptions from the retry function
        raise
    except Exception as e:
        loggers.error(f"Unexpected error in TTS generation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/tts/local")
async def generate_local_tts_audio(
    text: str = Body(..., description="Text to convert to speech"),
    gender: str = Body("ne_female", description="Gender/voice type"),
    speaker: int = Body(0, description="Speaker ID"),
    length_scale: float = Body(1.0, description="Length scale for speech speed"),
    noise_scale: float = Body(0.667, description="Noise scale"),
    noise_w: float = Body(0.8, description="Noise width"),
    sentence_silence: float = Body(0, description="Silence between sentences"),
    input_delay: float = Body(0, description="Input delay"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Generate audio from text using local TTS API
    """
    try:
        loggers.info(f"Local TTS request from user: {current_user.user.username} for text: {text[:50]}...")

        # Prepare form data for the local TTS API
        form_data = {
            'text': text,
            'gender': gender,
            'speaker': str(speaker),
            'length_scale': str(length_scale),
            'noise_scale': str(noise_scale),
            'noise_w': str(noise_w),
            'sentence_silence': str(sentence_silence),
            'input_delay': str(input_delay)
        }

        # Make the API call to local TTS service
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                'http://*************:5000/tts',
                headers={
                    'accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data=form_data
            )

            if response.status_code != 200:
                loggers.error(f"Local TTS API error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Local TTS API error: {response.text}"
                )

            # Get the audio content
            audio_data = response.content

            if not audio_data:
                raise HTTPException(status_code=500, detail="No audio data received from local TTS API")

            # Determine content type from response headers
            content_type = response.headers.get('content-type', 'audio/wav')

            # Determine file extension based on content type
            if "mp3" in content_type:
                file_ext = "mp3"
            elif "wav" in content_type:
                file_ext = "wav"
            elif "ogg" in content_type:
                file_ext = "ogg"
            elif "aac" in content_type:
                file_ext = "aac"
            else:
                file_ext = "audio"

            loggers.info(f"Local TTS generated audio: {len(audio_data)} bytes, content type: {content_type}")

            # Return the audio as a streaming response
            return StreamingResponse(
                io.BytesIO(audio_data),
                media_type=content_type,
                headers={
                    "Content-Disposition": f"attachment; filename=local_tts_audio.{file_ext}",
                    "Content-Length": str(len(audio_data)),
                    "Accept-Ranges": "bytes"
                }
            )

    except httpx.RequestError as e:
        loggers.error(f"HTTP request error to local TTS: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Request error: {str(e)}")
    except Exception as e:
        loggers.error(f"Unexpected error in local TTS generation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/tts/local/play")
async def play_local_tts_audio(
    text: str = Body(..., description="Text to convert to speech"),
    gender: str = Body("ne_female", description="Gender/voice type"),
    speaker: int = Body(0, description="Speaker ID"),
    length_scale: float = Body(1.0, description="Length scale for speech speed"),
    noise_scale: float = Body(0.667, description="Noise scale"),
    noise_w: float = Body(0.8, description="Noise width"),
    sentence_silence: float = Body(0, description="Silence between sentences"),
    input_delay: float = Body(0, description="Input delay"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Generate audio from text using local TTS API and return for direct playback
    """
    try:
        loggers.info(f"Local TTS play request from user: {current_user.user.username} for text: {text[:50]}...")

        # Prepare form data for the local TTS API
        form_data = {
            'text': text,
            'gender': gender,
            'speaker': str(speaker),
            'length_scale': str(length_scale),
            'noise_scale': str(noise_scale),
            'noise_w': str(noise_w),
            'sentence_silence': str(sentence_silence),
            'input_delay': str(input_delay)
        }

        # Make the API call to local TTS service
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                'http://*************:5000/tts',
                headers={
                    'accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data=form_data
            )

            if response.status_code != 200:
                loggers.error(f"Local TTS API error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Local TTS API error: {response.text}"
                )

            # Get the audio content
            audio_data = response.content

            if not audio_data:
                raise HTTPException(status_code=500, detail="No audio data received from local TTS API")

            # Determine content type from response headers
            content_type = response.headers.get('content-type', 'audio/wav')

            # Force WAV content type for better browser compatibility
            if not content_type or content_type == 'application/json':
                content_type = 'audio/wav'

            loggers.info(f"Local TTS generated audio: {len(audio_data)} bytes, original content type: {response.headers.get('content-type')}, using: {content_type}")

            # Return the audio for direct playback (inline) with browser-friendly headers
            return StreamingResponse(
                io.BytesIO(audio_data),
                media_type=content_type,
                headers={
                    "Content-Disposition": "inline; filename=audio.wav",
                    "Content-Length": str(len(audio_data)),
                    "Accept-Ranges": "bytes",
                    "Cache-Control": "no-cache, no-store, must-revalidate",
                    "Pragma": "no-cache",
                    "Expires": "0",
                    "Access-Control-Allow-Origin": "*",
                    "Cross-Origin-Resource-Policy": "cross-origin"
                }
            )

    except httpx.RequestError as e:
        loggers.error(f"HTTP request error to local TTS: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Request error: {str(e)}")
    except Exception as e:
        loggers.error(f"Unexpected error in local TTS generation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/tts/local/debug")
async def debug_local_tts_response(
    text: str = Body("test", description="Text to convert to speech"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Debug endpoint to check what the local TTS API returns
    """
    try:
        loggers.info(f"Debug local TTS request from user: {current_user.user.username}")

        # Prepare form data for the local TTS API
        form_data = {
            'text': text,
            'gender': 'ne_female',
            'speaker': '0',
            'length_scale': '1.0',
            'noise_scale': '0.667',
            'noise_w': '0.8',
            'sentence_silence': '0',
            'input_delay': '0'
        }

        # Make the API call to local TTS service
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                'http://*************:5000/tts',
                headers={
                    'accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data=form_data
            )

            # Return debug information
            return {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content_length": len(response.content),
                "content_type": response.headers.get('content-type'),
                "is_audio": len(response.content) > 1000,  # Assume audio if > 1KB
                "first_bytes": response.content[:50].hex() if response.content else None,
                "response_text_preview": response.text[:200] if response.headers.get('content-type', '').startswith('text') else "Binary content"
            }

    except Exception as e:
        loggers.error(f"Debug error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Debug error: {str(e)}")


@router.post("/tts/local/stream")
async def stream_local_tts_audio(
    text: str = Body(..., description="Text to convert to speech"),
    gender: str = Body("ne_female", description="Gender/voice type"),
    speaker: int = Body(0, description="Speaker ID"),
    length_scale: float = Body(1.0, description="Length scale for speech speed"),
    noise_scale: float = Body(0.667, description="Noise scale"),
    noise_w: float = Body(0.8, description="Noise width"),
    sentence_silence: float = Body(0, description="Silence between sentences"),
    input_delay: float = Body(0, description="Input delay"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Stream audio from local TTS API with optimized headers for web playback
    """
    try:
        loggers.info(f"Local TTS stream request from user: {current_user.user.username} for text: {text[:50]}...")

        # Prepare form data for the local TTS API
        form_data = {
            'text': text,
            'gender': gender,
            'speaker': str(speaker),
            'length_scale': str(length_scale),
            'noise_scale': str(noise_scale),
            'noise_w': str(noise_w),
            'sentence_silence': str(sentence_silence),
            'input_delay': str(input_delay)
        }

        # Make the API call to local TTS service
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                'http://*************:5000/tts',
                headers={
                    'accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data=form_data
            )

            if response.status_code != 200:
                loggers.error(f"Local TTS API error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Local TTS API error: {response.text}"
                )

            # Get the audio content
            audio_data = response.content

            if not audio_data:
                raise HTTPException(status_code=500, detail="No audio data received from local TTS API")

            loggers.info(f"Local TTS stream: {len(audio_data)} bytes, original headers: {dict(response.headers)}")

            # Return optimized streaming response for web audio playback
            return StreamingResponse(
                io.BytesIO(audio_data),
                media_type="audio/wav",  # Force WAV for maximum compatibility
                headers={
                    "Accept-Ranges": "bytes",
                    "Content-Length": str(len(audio_data)),
                    "Cache-Control": "no-cache",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "*",
                    "Cross-Origin-Embedder-Policy": "require-corp",
                    "Cross-Origin-Resource-Policy": "cross-origin"
                }
            )

    except httpx.RequestError as e:
        loggers.error(f"HTTP request error to local TTS: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Request error: {str(e)}")
    except Exception as e:
        loggers.error(f"Unexpected error in local TTS stream: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")