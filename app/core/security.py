# src/core/security.py

from datetime import datetime, timedelta
from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError
# from passlib.context import CryptContext
from fastapi.security import OAuth2<PERSON>asswordBearer
from fastapi import Depends, HTTPException

import jwt
from app.core.config import SECRET_KEY, ALGORITHM
from app.models.user import UserTenantDB, User
from app.models.permission import Permission
from app.models.role import Role
from app.core.database import get_db_from_tenant_id
from typing import Optional


ph = PasswordHasher()
# pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

def create_access_token(data: dict, expires_delta:timedelta = None):
    to_encode = data.copy() 
    if expires_delta:   
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=1)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_tenant_info(token: str = Depends(oauth2_scheme)) -> UserTenantDB:
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        # print("payload: ", payload)
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception
    
    tenant_db = get_db_from_tenant_id(payload.get("tenant_id"))
    user = tenant_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception

    # user_permissions = [Permission(**tenant_db.permissions.find_one({"name": permission})) for permission in user["permissions"]]
    # user["permissions"] = user_permissions

    # user_role = Role(**tenant_db.roles.find_one({"name": user["role"]}))
    # user["role"] = user_role
    
    
    return UserTenantDB(tenant_id=payload.get("tenant_id"), db=tenant_db, user=User(**user))


def min_role(min_role: str):
    async def check_role(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        user_role = user_tenant_info.user.role
        ROLES = user_tenant_info.db.settings.find_one({"name":"role_hierarchy"})['roles']
        if ROLES[user_role] < ROLES[min_role]:
            raise HTTPException(status_code=403, detail="You are not authorized to carry out this action")
        return user_tenant_info
    return check_role

def create_invitation_token(username: str, role: str, invited_by: str, tenant_id:str, expires_delta: Optional[timedelta] = None):
    """
    Creates a JWT token for agent invitation.
    """
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id}
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts the agent's name.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        invited_by: str = payload.get("invited_by")
        role: str = payload.get("role")
        result = get_db_from_tenant_id(payload.get("tenant_id")).invitations.find_one({"username": username, "role": role})

        if username is None or invited_by is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")
        
        return username, invited_by, role
    
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")


# Replace CryptContext with PasswordHasher
def hash_password(password: str) -> str:
    """
    Hash new passwords using Argon2
    """
    return ph.hash(password)
    
# Update the verify_password function
def verify_password(plain_password, hashed_password):
    """
    Verifies a plain password against a hashed password.
    """
    try:
        return ph.verify(hashed_password, plain_password)
    except VerifyMismatchError:
        return False


def require_permissions(required_permissions: list[str]):
    """
    Dependency that checks if the user has all the required permissions.
    Usage: @router.get("/endpoint", dependencies=[Depends(require_permissions(["read:users", "write:users"]))])
    """
    async def check_permissions(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        user_permissions = [p.name for p in user_tenant_info.user.permissions]
        
        missing_permissions = [perm for perm in required_permissions if perm not in user_permissions]
        
        if missing_permissions:
            raise HTTPException(
                status_code=403,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )
        
        return user_tenant_info
    return check_permissions


