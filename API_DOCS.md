# API Usage Documentation

## Authentication
Most endpoints require authentication. First, log in to get a JWT token.

---

## 1. Login

**POST** `/login`

**Request:**
```bash
curl -X 'POST' \
  'https://int-api.nextai.asia/v1/login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'grant_type=password&username=superadmin&password=godmod&scope=&client_id=apitest'
```

**Response:**
```json
{
  "id": "67f7479290fe4ef9d1369753",
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjg2NjcxMDU0YWI5MDRmYTRiNjM3YmM3IiwiZXhwIjoxNzUxNjM0NjE5fQ.VTUtxUnHUAL5xGDS9XjWyTx-H3KKfdHIiu1m8OIVnew",
  "token_type": "bearer",
  "username": "superadmin",
  "role": "admin",
  "tenant_id": "686671054ab904fa4b637bc7",
  "tenant_label": "apitest",
  "tenant_slug": "apitest"
}
```

Use the `access_token` as a Bearer token in the `Authorization` header for subsequent requests.

---

## 2. Set Google API Key

**POST** `/configs/google-api-key`

**Request:**
```bash
curl -X 'POST' \
  'https://int-api.nextai.asia/v1/configs/google-api-key' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjg2NjcxMDU0YWI5MDRmYTRiNjM3YmM3IiwiZXhwIjoxNzUxNjM0NjY1fQ.uXB059-5Z_EnTPv2cZRtFf70XqHO0JVw68crtaL2w2Y' \
  -H 'Content-Type: application/json' \
  -d '{
  "google_api_key": "string"
}'
```

**Response:**
```json
{
  "message": "Google API key set successfully"
}
```

---

## 3. Get Google API Key

**GET** `/configs/google-api-key`

**Request:**
```bash
curl -X 'GET' \
  'https://int-api.nextai.asia/v1/configs/google-api-key' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjg2NjcxMDU0YWI5MDRmYTRiNjM3YmM3IiwiZXhwIjoxNzUxNjM0NjY1fQ.uXB059-5Z_EnTPv2cZRtFf70XqHO0JVw68crtaL2w2Y'
```

**Response:**
```json
{
  "GOOGLE_API_KEY": "YOUR_GOOGLE_API_KEY"
}
```

---

## 4. Get All Prompts

**GET** `/prompts`

**Request:**
```bash
curl -X 'GET' \
  'https://int-api.nextai.asia/v1/prompts' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjg2NjcxMDU0YWI5MDRmYTRiNjM3YmM3IiwiZXhwIjoxNzUxNjM0NjY1fQ.uXB059-5Z_EnTPv2cZRtFf70XqHO0JVw68crtaL2w2Y'
```

**Response:**
```json
{
  "prompts": [
    {
      "_id": "...",
      "name": "...",
      "text": "...",
      "model": "..."
    },
    ...
  ]
}
```

---

## 5. Add New Prompt

**POST** `/prompts`

**Request:**
```bash
curl -X 'POST' \
  'https://int-api.nextai.asia/v1/prompts' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjg2NjcxMDU0YWI5MDRmYTRiNjM3YmM3IiwiZXhwIjoxNzUxNjM0NjY1fQ.uXB059-5Z_EnTPv2cZRtFf70XqHO0JVw68crtaL2w2Y' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "string",
  "text": "string",
  "model": "string"
}'
```

**Response:**
```json
{
  "message": "Prompt added successfully",
  "prompt_id": "686681dbf5525413295f4edc"
}
```

---

## 6. Edit Prompt

**PUT** `/prompts/{prompt_id}`

**Request:**
```bash
curl -X 'PUT' \
  'https://int-api.nextai.asia/v1/prompts/686681dbf5525413295f4edc' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjg2NjcxMDU0YWI5MDRmYTRiNjM3YmM3IiwiZXhwIjoxNzUxNjM0NjY1fQ.uXB059-5Z_EnTPv2cZRtFf70XqHO0JVw68crtaL2w2Y' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "sdfsdfsdf",
  "text": "string",
  "model": "string"
}'
```

**Response:**
```json
{
  "message": "Prompt updated successfully"
}
```

---

## 7. Delete Prompt

**DELETE** `/prompts/{prompt_id}`

**Request:**
```bash
curl -X 'DELETE' \
  'https://int-api.nextai.asia/v1/prompts/686681dbf5525413295f4edc' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjg2NjcxMDU0YWI5MDRmYTRiNjM3YmM3IiwiZXhwIjoxNzUxODY1Nzg2fQ.RwBlOf_UeRTN2Kp8wE9TGDemllNoHG7FQGtJYJYelLo'
```

**Response:**
```json
{
  "message": "Prompt deleted successfully"
}
```

---

## 8. Get Voices

**GET** `/voices`

**Request:**
```bash
curl -X 'GET' \
  'https://int-api.nextai.asia/v1/voices' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjg2NjcxMDU0YWI5MDRmYTRiNjM3YmM3IiwiZXhwIjoxNzUxODY1Nzg2fQ.RwBlOf_UeRTN2Kp8wE9TGDemllNoHG7FQGtJYJYelLo'
```

**Response:**
```json
{
  "voices": [
    {
      "_id": "686678d04ab904fa4b637bd9",
      "name": "Sadaltager"
    },
    {
      "_id": "686678e34ab904fa4b637bda",
      "name": "Aoede"
    }
  ]
}
```

---

## Notes
- Replace `<JWT_TOKEN>` with your actual token from the login response.
- Replace `<PROMPT_ID>` with the actual prompt ID.
- All endpoints return JSON responses.
