
# 🔌 gRPC Model Inference with Python (Server + Client)

This project demonstrates how to use **gRPC** for sending inference requests from a REST API or client to a **Python-based GPU model server**.

---

## 📁 Project Structure

```

grpc\_example/
├── proto/
│   ├── model.proto
│   ├── model\_pb2.py
│   └── model\_pb2\_grpc.py
├── server/
│   └── server.py
├── client/
│   └── client.py

````

---

## 📦 Requirements

Install required packages:

```bash
pip install grpcio grpcio-tools fastapi uvicorn
````

---

## 📜 1. The `.proto` file (`proto/model.proto`)

```proto
syntax = "proto3";

service ModelService {
  rpc Infer (InferRequest) returns (InferResponse);
}

message InferRequest {
  string input_text = 1;
}

message InferResponse {
  string output_text = 1;
}
```

This defines:

* A service: `ModelService`
* A method: `Infer()`, which takes a `string` and returns a `string`

---

## 🔧 2. Compile the `.proto` File

### 📌 On **both server and client**, compile like this:

```bash
cd proto/
python -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. model.proto
```

This generates:

* `model_pb2.py`
* `model_pb2_grpc.py`

✅ These must be the **same** on both client and server.

---

## 🧠 3. Server Code (`server/server.py`)

```python
import grpc
from concurrent import futures
import time

import sys
sys.path.append("../proto")
import model_pb2
import model_pb2_grpc

class ModelService(model_pb2_grpc.ModelServiceServicer):
    def Infer(self, request, context):
        input_text = request.input_text
        output_text = f"Processed '{input_text}' on server"
        return model_pb2.InferResponse(output_text=output_text)

def serve():
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=4))
    model_pb2_grpc.add_ModelServiceServicer_to_server(ModelService(), server)
    server.add_insecure_port("[::]:50051")
    print("gRPC Server running on port 50051")
    server.start()
    server.wait_for_termination()

if __name__ == "__main__":
    serve()
```

### ▶️ Run the Server

```bash
python server/server.py
```

---

## 💬 4. Client Code (`client/client.py`)

```python
import grpc
import sys
sys.path.append("../proto")

import model_pb2
import model_pb2_grpc

def main():
    channel = grpc.insecure_channel("localhost:50051")  # Use server IP if remote
    stub = model_pb2_grpc.ModelServiceStub(channel)

    request = model_pb2.InferRequest(input_text="Hello from client")
    response = stub.Infer(request)
    print("Response from server:", response.output_text)

if __name__ == "__main__":
    main()
```

### ▶️ Run the Client

```bash
python client/client.py
```

---

## 📌 Notes

* Always keep `model.proto` and the generated `model_pb2*.py` files **synchronized**.
* Use `sys.path.append()` or proper packaging to ensure import paths work.
* For multi-service systems, you can extend the `.proto` with:

  * `bytes` for file/image/audio data
  * `map<string, string>` for dict-like metadata
  * `repeated` fields for arrays/batch processing
  * Streaming (bi-directional) RPCs for real-time scenarios

---

## ✅ Output Example

```bash
# On the client:
Response from server: Processed 'Hello from client' on server
```

---

## 🧰 Resources

* [gRPC Python Docs](https://grpc.io/docs/languages/python/)
* [Protocol Buffers Language Guide](https://protobuf.dev/programming-guides/proto3/)

---

## 📎 License

MIT License

```

---

Would you like me to generate this as a downloadable `.zip` project scaffold or push to a GitHub repo format?
```
